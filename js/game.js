// Main game engine

class Game {
    constructor() {
        this.canvas = null;
        this.ctx = null;
        this.running = false;
        this.paused = false;
        
        // Game state
        this.state = 'menu'; // menu, playing, paused, gameOver, levelComplete
        this.selectedCharacter = null;
        this.player = null;
        this.currentLevel = null;
        this.levelIndex = 0;
        
        // Systems
        this.collisionSystem = new CollisionSystem();
        this.events = new GameUtils.EventEmitter();
        
        // Game data
        this.levels = [];
        this.gameStats = {
            totalScore: 0,
            totalTime: 0,
            levelsCompleted: 0,
            enemiesDefeated: 0
        };
        
        // Timing
        this.lastTime = 0;
        this.deltaTime = 0;
        this.targetFPS = 60;
        this.frameTime = 1000 / this.targetFPS;
        
        // Debug
        this.debug = false;
        this.showFPS = false;
        this.fps = 0;
        this.frameCount = 0;
        this.fpsTimer = 0;
        
        // Initialize
        this.setupEventListeners();
        this.createLevels();
    }
    
    init(canvasId) {
        this.canvas = document.getElementById(canvasId);
        if (!this.canvas) {
            console.error('Canvas not found:', canvasId);
            return false;
        }
        
        this.ctx = this.canvas.getContext('2d');
        this.ctx.imageSmoothingEnabled = false; // Pixel art style
        
        // Set up UI event listeners
        this.setupUIEventListeners();
        
        return true;
    }
    
    setupEventListeners() {
        // Game events
        this.events.on('characterSelected', (data) => {
            this.selectedCharacter = data.character;
            this.startGame();
        });
        
        this.events.on('levelComplete', () => {
            if (window.audioManager) window.audioManager.playSound('levelComplete');
            this.onLevelComplete();
        });

        this.events.on('gameOver', () => {
            if (window.audioManager) window.audioManager.playSound('gameOver');
            this.onGameOver();
        });

        this.events.on('timeUp', () => {
            if (window.audioManager) window.audioManager.playSound('gameOver');
            this.onTimeUp();
        });

        this.events.on('checkpointActivated', (data) => {
            if (window.audioManager) window.audioManager.playSound('powerup');
            this.onCheckpointActivated(data.checkpoint);
        });

        this.events.on('enemyDeath', (data) => {
            if (window.audioManager) window.audioManager.playSound('enemyDeath');
            this.gameStats.enemiesDefeated++;
        });
        
        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            switch (e.key) {
                case 'p':
                case 'P':
                    if (this.state === 'playing') {
                        this.togglePause();
                    }
                    break;
                case 'r':
                case 'R':
                    if (this.state === 'gameOver') {
                        this.restartGame();
                    }
                    break;
                case 'F1':
                    e.preventDefault();
                    this.debug = !this.debug;
                    break;
                case 'F2':
                    e.preventDefault();
                    this.showFPS = !this.showFPS;
                    break;
                case 'm':
                case 'M':
                    if (window.audioManager) {
                        window.audioManager.toggleMute();
                    }
                    break;
            }
        });
    }
    
    setupUIEventListeners() {
        // Character selection
        const characterCards = document.querySelectorAll('.character-card');
        characterCards.forEach(card => {
            card.addEventListener('click', () => {
                // Remove previous selection
                characterCards.forEach(c => c.classList.remove('selected'));
                // Add selection to clicked card
                card.classList.add('selected');
                
                const characterType = card.dataset.character;
                document.getElementById('startGame').disabled = false;
                
                // Store selected character
                this.selectedCharacter = characterType;
            });
        });
        
        // Start game button
        document.getElementById('startGame').addEventListener('click', () => {
            if (this.selectedCharacter) {
                this.events.emit('characterSelected', { character: this.selectedCharacter });
            }
        });
        
        // Restart button
        document.getElementById('restartGame').addEventListener('click', () => {
            this.restartGame();
        });
        
        // Back to menu button
        document.getElementById('backToMenu').addEventListener('click', () => {
            this.backToMenu();
        });
    }
    
    createLevels() {
        // Level 1: Tutorial/Easy
        this.levels.push({
            name: "Getting Started",
            theme: "office",
            width: 1500,
            height: 576,
            startPosition: { x: 50, y: 450 },
            goalPosition: { x: 1400, y: 450 },
            platforms: [
                { x: 300, y: 450, width: 200, height: 20 },
                { x: 600, y: 400, width: 150, height: 20 },
                { x: 900, y: 350, width: 200, height: 20 },
                { x: 1200, y: 300, width: 150, height: 20 }
            ],
            enemies: [
                { x: 400, y: 430, type: 'procrastination' },
                { x: 700, y: 380, type: 'procrastination' },
                { x: 1000, y: 330, type: 'procrastination' }
            ],
            powerups: [
                { x: 350, y: 430, type: 'motivation', value: 20 },
                { x: 650, y: 380, type: 'score', value: 100 },
                { x: 950, y: 330, type: 'health', value: 25 }
            ],
            checkpoints: [
                { x: 500, y: 450 },
                { x: 1000, y: 350 }
            ]
        });
        
        // Level 2: Intermediate
        this.levels.push({
            name: "The Daily Grind",
            theme: "office",
            width: 2000,
            height: 576,
            startPosition: { x: 50, y: 450 },
            goalPosition: { x: 1900, y: 150 },
            platforms: [
                { x: 200, y: 450, width: 100, height: 20 },
                { x: 400, y: 400, width: 100, height: 20 },
                { x: 600, y: 350, width: 150, height: 20 },
                { x: 850, y: 300, width: 100, height: 20 },
                { x: 1050, y: 250, width: 200, height: 20 },
                { x: 1350, y: 300, width: 100, height: 20 },
                { x: 1550, y: 250, width: 150, height: 20 },
                { x: 1800, y: 200, width: 150, height: 20 }
            ],
            enemies: [
                { x: 250, y: 430, type: 'procrastination' },
                { x: 450, y: 380, type: 'distraction' },
                { x: 700, y: 330, type: 'procrastination' },
                { x: 1100, y: 230, type: 'distraction' },
                { x: 1400, y: 280, type: 'burnout' },
                { x: 1600, y: 230, type: 'procrastination' }
            ],
            powerups: [
                { x: 450, y: 380, type: 'motivation', value: 30 },
                { x: 900, y: 280, type: 'jump', value: 1 },
                { x: 1200, y: 230, type: 'health', value: 30 },
                { x: 1600, y: 230, type: 'score', value: 200 }
            ],
            checkpoints: [
                { x: 600, y: 350 },
                { x: 1200, y: 250 }
            ]
        });
        
        // Level 3: Hard
        this.levels.push({
            name: "Crunch Time",
            theme: "office",
            width: 2500,
            height: 576,
            timeLimit: 240000, // 4 minutes
            startPosition: { x: 50, y: 450 },
            goalPosition: { x: 2400, y: 100 },
            platforms: [
                { x: 150, y: 450, width: 80, height: 15 },
                { x: 300, y: 400, width: 80, height: 15 },
                { x: 450, y: 350, width: 100, height: 15 },
                { x: 650, y: 300, width: 80, height: 15 },
                { x: 800, y: 250, width: 120, height: 15 },
                { x: 1000, y: 300, width: 80, height: 15 },
                { x: 1200, y: 250, width: 100, height: 15 },
                { x: 1400, y: 200, width: 80, height: 15 },
                { x: 1600, y: 250, width: 120, height: 15 },
                { x: 1850, y: 200, width: 100, height: 15 },
                { x: 2050, y: 150, width: 150, height: 15 },
                { x: 2300, y: 150, width: 150, height: 15 }
            ],
            enemies: [
                { x: 200, y: 430, type: 'distraction' },
                { x: 350, y: 380, type: 'procrastination' },
                { x: 500, y: 330, type: 'distraction' },
                { x: 700, y: 280, type: 'burnout' },
                { x: 850, y: 230, type: 'procrastination' },
                { x: 1050, y: 280, type: 'distraction' },
                { x: 1250, y: 230, type: 'burnout' },
                { x: 1450, y: 180, type: 'procrastination' },
                { x: 1700, y: 230, type: 'distraction' },
                { x: 1900, y: 180, type: 'burnout' },
                { x: 2100, y: 130, type: 'procrastination' }
            ],
            powerups: [
                { x: 500, y: 330, type: 'motivation', value: 40 },
                { x: 850, y: 230, type: 'health', value: 50 },
                { x: 1250, y: 230, type: 'speed', value: 2 },
                { x: 1900, y: 180, type: 'motivation', value: 50 },
                { x: 2100, y: 130, type: 'score', value: 500 }
            ],
            checkpoints: [
                { x: 650, y: 300 },
                { x: 1200, y: 250 },
                { x: 1850, y: 200 }
            ]
        });
    }
    
    startGame() {
        try {
            console.log('=== START GAME DEBUG ===');
            console.log('Selected character:', this.selectedCharacter);
            console.log('Available classes:', {
                Character: typeof Character,
                GameUtils: typeof GameUtils,
                PhysicsSystem: typeof PhysicsSystem,
                LevelSystem: typeof LevelSystem,
                EnemySystem: typeof EnemySystem
            });

            if (!this.selectedCharacter) {
                console.error('No character selected');
                return;
            }

            console.log('1. Hiding menu, showing game HUD...');
            // Hide menu, show game
            document.getElementById('characterSelect').classList.add('hidden');
            document.getElementById('gameHUD').classList.remove('hidden');

            console.log('2. Checking Character class...');
            // Check if Character class is available
            if (typeof Character === 'undefined') {
                console.error('Character class not found');
                return;
            }

            console.log('3. Checking GameUtils...');
            if (typeof GameUtils === 'undefined') {
                console.error('GameUtils not found');
                return;
            }

            console.log('4. Creating character...');
            // Create player character
            this.player = new Character(this.selectedCharacter, 0, 0);
            console.log('Character created successfully:', this.player);

            console.log('5. Loading level...');
            // Load first level
            this.levelIndex = 0;
            this.loadLevel(this.levelIndex);

            console.log('6. Updating UI...');
            // Update UI
            document.getElementById('characterName').textContent = this.player.name;

            console.log('7. Starting game loop...');
            // Start game loop
            this.state = 'playing';
            this.running = true;
            this.gameLoop();

            console.log('=== GAME STARTED SUCCESSFULLY ===');

        } catch (error) {
            console.error('=== ERROR STARTING GAME ===');
            console.error('Error details:', error);
            console.error('Stack trace:', error.stack);
            alert('Error starting game: ' + error.message + '\nCheck console for details.');
        }
    }
    
    loadLevel(index) {
        try {
            console.log('=== LOAD LEVEL DEBUG ===');
            console.log('Loading level index:', index);
            console.log('Total levels available:', this.levels.length);

            if (index >= this.levels.length) {
                console.log('No more levels, completing game');
                this.onGameComplete();
                return;
            }

            console.log('Level data:', this.levels[index]);

            // Check if LevelSystem is available
            console.log('LevelSystem type:', typeof LevelSystem);
            if (typeof LevelSystem === 'undefined') {
                throw new Error('LevelSystem not found');
            }

            console.log('LevelSystem.Level type:', typeof LevelSystem.Level);
            if (typeof LevelSystem.Level === 'undefined') {
                throw new Error('LevelSystem.Level not found');
            }

            console.log('Creating level...');
            this.currentLevel = new LevelSystem.Level(this.levels[index]);
            console.log('Level created successfully:', this.currentLevel);

            console.log('Getting spawn position...');
            const startPos = this.currentLevel.getSpawnPosition();
            console.log('Spawn position:', startPos);

            console.log('Positioning player...');
            this.player.position.x = startPos.x;
            this.player.position.y = startPos.y;
            this.player.velocity.x = 0;
            this.player.velocity.y = 0;

            console.log('Player positioned at:', this.player.position);
            console.log('Ground Y:', GameUtils.GAME_CONFIG.GROUND_Y);
            console.log('Level platforms:', this.currentLevel.platforms.length);

            console.log('Updating UI...');
            document.getElementById('levelValue').textContent = index + 1;

            console.log('=== LEVEL LOADED SUCCESSFULLY ===');

        } catch (error) {
            console.error('=== ERROR LOADING LEVEL ===');
            console.error('Error object:', error);
            console.error('Error message:', error ? error.message : 'Unknown error');
            console.error('Error stack:', error ? error.stack : 'No stack trace');
            alert('Error loading level: ' + (error ? error.message : 'Unknown error'));
        }
    }
    
    gameLoop(currentTime = 0) {
        if (!this.running) return;
        
        // Calculate delta time
        this.deltaTime = currentTime - this.lastTime;
        this.lastTime = currentTime;
        
        // Cap delta time to prevent large jumps
        this.deltaTime = Math.min(this.deltaTime, this.frameTime * 2);
        
        // Update FPS counter
        this.updateFPS(this.deltaTime);
        
        if (!this.paused && this.state === 'playing') {
            this.update(this.deltaTime);
        }
        
        this.render();
        
        // Continue loop
        requestAnimationFrame((time) => this.gameLoop(time));
    }
    
    update(deltaTime) {
        if (!this.player || !this.currentLevel) return;
        
        // Update player
        this.player.update(deltaTime);
        
        // Update level
        this.currentLevel.update(deltaTime, this.player);
        
        // Update collision system
        const allObjects = [this.player, ...this.currentLevel.getAllObjects()];
        this.collisionSystem.update(allObjects);
        
        // Update input manager
        window.inputManager.update();
        
        // Update UI
        this.updateUI();
        
        // Update game stats
        this.gameStats.totalTime += deltaTime;
    }
    
    render() {
        if (!this.ctx) return;
        
        // Clear canvas
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        if (this.currentLevel) {
            // Apply camera transform once for everything
            this.ctx.save();
            this.ctx.translate(-this.currentLevel.camera.x, -this.currentLevel.camera.y);

            // Render background and platforms (without camera transform in level)
            this.renderLevelBackground();

            // Render level objects
            for (const platform of this.currentLevel.platforms) {
                platform.render(this.ctx);
            }

            // Render powerups
            for (const powerup of this.currentLevel.powerups) {
                powerup.render(this.ctx);
            }

            // Render checkpoints
            for (const checkpoint of this.currentLevel.checkpoints) {
                checkpoint.render(this.ctx);
            }

            // Render goal
            if (this.currentLevel.goal) {
                this.currentLevel.goal.render(this.ctx);
            }

            // Render enemies
            for (const enemy of this.currentLevel.enemies) {
                enemy.render(this.ctx);
            }

            // Render player
            if (this.player) {
                this.player.render(this.ctx);
            }

            this.ctx.restore();
        }
        
        // Render debug info
        if (this.debug) {
            this.renderDebugInfo();
        }
        
        // Render FPS
        if (this.showFPS) {
            this.renderFPS();
        }
        
        // Render pause overlay
        if (this.paused) {
            this.renderPauseOverlay();
        }
    }

    renderLevelBackground() {
        if (!this.currentLevel) return;

        // Simple gradient background
        const gradient = this.ctx.createLinearGradient(0, 0, 0, this.currentLevel.height);
        gradient.addColorStop(0, this.currentLevel.backgroundColor);
        gradient.addColorStop(1, '#4682B4');

        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.currentLevel.width, this.currentLevel.height);

        // Add theme elements
        this.renderThemeElements();
    }

    renderThemeElements() {
        if (!this.currentLevel) return;

        switch (this.currentLevel.theme) {
            case 'office':
                // Draw simple office buildings in background
                this.ctx.fillStyle = 'rgba(100, 100, 100, 0.3)';
                for (let i = 0; i < 5; i++) {
                    const x = (this.currentLevel.width / 5) * i;
                    const height = GameUtils.randomBetween(100, 200);
                    this.ctx.fillRect(x, this.currentLevel.height - height - 76, this.currentLevel.width / 5, height);
                }
                break;
        }
    }

    updateUI() {
        if (!this.player) return;
        
        document.getElementById('scoreValue').textContent = this.player.score;
        document.getElementById('livesValue').textContent = this.player.lives;
        
        // Update motivation bar
        const motivationPercent = (this.player.motivation / this.player.maxMotivation) * 100;
        document.getElementById('motivationBar').style.width = motivationPercent + '%';
    }
    
    updateFPS(deltaTime) {
        this.frameCount++;
        this.fpsTimer += deltaTime;
        
        if (this.fpsTimer >= 1000) {
            this.fps = Math.round(this.frameCount * 1000 / this.fpsTimer);
            this.frameCount = 0;
            this.fpsTimer = 0;
        }
    }
    
    renderDebugInfo() {
        if (!this.player) return;
        
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
        this.ctx.fillRect(10, 10, 200, 120);
        
        this.ctx.fillStyle = 'white';
        this.ctx.font = '12px monospace';
        this.ctx.fillText(`Player: ${this.player.position.x.toFixed(1)}, ${this.player.position.y.toFixed(1)}`, 15, 25);
        this.ctx.fillText(`Velocity: ${this.player.velocity.x.toFixed(1)}, ${this.player.velocity.y.toFixed(1)}`, 15, 40);
        this.ctx.fillText(`Grounded: ${this.player.isGrounded}`, 15, 55);
        this.ctx.fillText(`State: ${this.player.state.currentState?.constructor.name || 'none'}`, 15, 70);
        this.ctx.fillText(`Health: ${this.player.health}/${this.player.maxHealth}`, 15, 85);
        this.ctx.fillText(`Motivation: ${this.player.motivation.toFixed(1)}`, 15, 100);
        this.ctx.fillText(`Enemies: ${this.currentLevel?.enemies.length || 0}`, 15, 115);
    }
    
    renderFPS() {
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
        this.ctx.fillRect(this.canvas.width - 80, 10, 70, 30);
        
        this.ctx.fillStyle = 'white';
        this.ctx.font = '14px monospace';
        this.ctx.fillText(`FPS: ${this.fps}`, this.canvas.width - 75, 30);
    }
    
    renderPauseOverlay() {
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        
        this.ctx.fillStyle = 'white';
        this.ctx.font = '48px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.fillText('PAUSED', this.canvas.width / 2, this.canvas.height / 2);
        this.ctx.font = '24px Arial';
        this.ctx.fillText('Press P to resume', this.canvas.width / 2, this.canvas.height / 2 + 50);
        this.ctx.textAlign = 'left';
    }
    
    // Event handlers
    onLevelComplete() {
        this.gameStats.levelsCompleted++;
        this.gameStats.totalScore += this.player.score;
        
        this.levelIndex++;
        if (this.levelIndex < this.levels.length) {
            // Load next level
            setTimeout(() => {
                this.loadLevel(this.levelIndex);
            }, 2000);
        } else {
            this.onGameComplete();
        }
    }
    
    onGameComplete() {
        this.state = 'gameComplete';
        document.getElementById('gameHUD').classList.add('hidden');
        document.getElementById('gameOver').classList.remove('hidden');
        document.getElementById('gameOverTitle').textContent = 'Congratulations!';
        document.getElementById('gameOverMessage').textContent = 'You have successfully retired early!';
        document.getElementById('finalScore').textContent = this.gameStats.totalScore;
    }
    
    onGameOver() {
        this.state = 'gameOver';
        document.getElementById('gameHUD').classList.add('hidden');
        document.getElementById('gameOver').classList.remove('hidden');
        document.getElementById('gameOverTitle').textContent = 'Game Over';
        document.getElementById('gameOverMessage').textContent = 'Procrastination got the better of you!';
        document.getElementById('finalScore').textContent = this.player.score;
    }
    
    onTimeUp() {
        this.onGameOver();
        document.getElementById('gameOverMessage').textContent = 'Time\'s up! You ran out of time!';
    }
    
    onCheckpointActivated(checkpoint) {
        // Save checkpoint position for respawn
        this.lastCheckpoint = checkpoint.position.copy();
    }
    
    togglePause() {
        this.paused = !this.paused;
    }
    
    restartGame() {
        // Reset game state
        this.state = 'playing';
        this.levelIndex = 0;
        this.gameStats = {
            totalScore: 0,
            totalTime: 0,
            levelsCompleted: 0,
            enemiesDefeated: 0
        };
        
        // Hide game over screen
        document.getElementById('gameOver').classList.add('hidden');
        document.getElementById('gameHUD').classList.remove('hidden');
        
        // Recreate player
        this.player = new Character(this.selectedCharacter, 0, 0);
        
        // Reload current level
        this.loadLevel(this.levelIndex);
    }
    
    backToMenu() {
        this.running = false;
        this.state = 'menu';
        
        // Hide all screens except character select
        document.getElementById('gameOver').classList.add('hidden');
        document.getElementById('gameHUD').classList.add('hidden');
        document.getElementById('characterSelect').classList.remove('hidden');
        
        // Reset selections
        document.querySelectorAll('.character-card').forEach(card => {
            card.classList.remove('selected');
        });
        document.getElementById('startGame').disabled = true;
        this.selectedCharacter = null;
    }
}

// Export for use in other files
window.Game = Game;
